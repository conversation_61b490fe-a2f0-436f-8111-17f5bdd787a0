import { useDispatch, useSelector } from "react-redux";
import { type RootState } from "../../redux/store.ts";
import { prevStep, goToStep } from "../../redux/slices/onboardingSlice.ts";
import { useState } from "react";
import {
  ReviewBusinessInfoSection,
  ReviewOwnerInfoSection,
  ReviewBankAccountSection,
  ComplianceCheckboxesSection,
  ImportantNoticeSection,
  SubmissionErrorSection,
  SubmissionSuccessPage,
  SubmissionLoadingOverlay,
} from "./sections";
import { useClientIp } from "./hooks/useClientIp";
import { useSubmitOnboarding } from "./hooks/useSubmitOnboarding";
import { type ComplianceState, type ComplianceValidationErrors } from "./utils/reviewValidation";

const OnboardingReview = () => {
  const dispatch = useDispatch();
  const { formData } = useSelector((state: RootState) => state.onboarding);
  const ipDetection = useClientIp();

  const [complianceState, setComplianceState] = useState<ComplianceState>({
    visaDisclosure: false,
    payrixTerms: false,
    bankDisclosures: false,
    attestationStatement: false,
    tcAttestation: false, // Syncs with attestationStatement
  });

  const { isSubmitting, submitError, submitSuccess, submitResult, validationErrors, handleSubmit, setValidationErrors } = useSubmitOnboarding({
    formData,
    complianceState,
    clientIp: ipDetection.ip,
  });

  const handleComplianceChange = (field: keyof ComplianceState, value: boolean) => {
    setComplianceState((prev) => ({
      ...prev,
      [field]: value,
      // Sync tcAttestation with attestationStatement
      ...(field === "attestationStatement" ? { tcAttestation: value } : {}),
    }));

    // Clear validation error when user checks the box
    if (value && validationErrors[field]) {
      setValidationErrors((prev: ComplianceValidationErrors) => ({ ...prev, [field]: undefined }));
    }
  };

  if (submitSuccess) {
    return <SubmissionSuccessPage submitResult={submitResult} />;
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {/* Header */}
          <div className="border-b border-gray-200 px-8 py-6">
            <h1 className="text-2xl font-semibold text-gray-900">Review & Submit</h1>
            <p className="text-gray-600 mt-1">Please review all information before submitting your application</p>
          </div>

          <div className="px-8 py-8">
            <ReviewBusinessInfoSection formData={formData} onEdit={() => dispatch(goToStep(1))} />

            <ReviewOwnerInfoSection formData={formData} onEdit={() => dispatch(goToStep(2))} />

            <ReviewBankAccountSection formData={formData} onEdit={() => dispatch(goToStep(3))} />

            {/* IP Detection Status */}
            <div className="border border-gray-200 rounded-lg p-6 mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Security Information</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">IP Address:</span>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-900 font-mono">{ipDetection.ip}</span>
                    {ipDetection.isLoading && (
                      <div className="flex items-center space-x-1">
                        <div className="w-3 h-3 border border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                        <span className="text-xs text-blue-600">Detecting...</span>
                      </div>
                    )}
                    {!ipDetection.isLoading && ipDetection.isValid && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">✓ Valid</span>
                    )}
                    {!ipDetection.isLoading && !ipDetection.isValid && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        ⚠ Fallback
                      </span>
                    )}
                  </div>
                </div>
                {ipDetection.source && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">Detection Source:</span>
                    <span className="text-sm text-gray-600 capitalize">{ipDetection.source}</span>
                  </div>
                )}
                {ipDetection.error && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                          <path
                            fillRule="evenodd"
                            d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm text-yellow-700">{ipDetection.error}</p>
                      </div>
                    </div>
                  </div>
                )}
                <p className="text-xs text-gray-500">
                  Your IP address is captured for security and compliance purposes as required by payment processing regulations.
                </p>
              </div>
            </div>

            <ComplianceCheckboxesSection complianceState={complianceState} validationErrors={validationErrors} onChange={handleComplianceChange} />

            <ImportantNoticeSection />

            {submitError && <SubmissionErrorSection error={submitError} />}

            {/* Navigation Buttons */}
            <div className="border-t border-gray-200 pt-6 flex justify-between">
              <button
                type="button"
                onClick={() => dispatch(prevStep())}
                disabled={isSubmitting}
                className="bg-gray-100 text-gray-700 px-8 py-3 rounded-lg font-medium hover:bg-gray-200 focus:ring-4 focus:ring-gray-200 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <button
                onClick={handleSubmit}
                disabled={isSubmitting || ipDetection.isLoading || (!ipDetection.isValid && ipDetection.source !== "fallback")}
                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                title={
                  ipDetection.isLoading
                    ? "Please wait while we detect your IP address..."
                    : !ipDetection.isValid && ipDetection.source !== "fallback"
                    ? "IP address detection failed. Please refresh the page and try again."
                    : undefined
                }
              >
                {isSubmitting && (
                  <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                    />
                  </svg>
                )}
                <span>{isSubmitting ? "Submitting..." : "Submit Application"}</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {isSubmitting && <SubmissionLoadingOverlay />}
    </div>
  );
};

export default OnboardingReview;
