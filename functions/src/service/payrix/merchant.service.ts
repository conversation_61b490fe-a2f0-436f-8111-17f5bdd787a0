import { AxiosError, AxiosInstance } from "axios";
import FormData from "form-data";
import { type OnboardingRequest } from "../../functions/merchants/schemas/onboarding.schema.js";
import { logger } from "../../helpers/logger.js";
import { PayrixMerchantResponse, PayrixMerchantEntity, MerchantValidationResult } from "../../types/payrix.types.js";
import { createPayrixApiClient, PAYRIX_API_URL } from "./api-client.js";
import { isApprovedMCCCode, getMCCCodeDetails } from "../../constants/approvedMccCodes.js";

export class PayrixMerchantService {
  private apiClient: AxiosInstance;

  constructor() {
    this.apiClient = createPayrixApiClient();
  }

  async checkMerchantExists(email: string, ein?: string): Promise<boolean> {
    try {
      logger.info("Checking for existing merchant", {
        email,
        ein: ein ? "[REDACTED]" : undefined,
      });

      const response = await this.apiClient.get(`/entities`);

      logger.info("Merchant existence check response", {
        status: response.status,
        dataLength: response.data?.response?.data?.length || 0,
      });

      const merchants: PayrixMerchantEntity[] = response.data?.response?.data || [];

      const emailExists = merchants.some((merchant: PayrixMerchantEntity) => merchant.email?.toLowerCase() === email.toLowerCase());

      return emailExists;
    } catch (error) {
      const axiosError = error as AxiosError;
      logger.error("Error checking merchant existence", {
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data,
        message: axiosError.message,
      });

      logger.warn("Merchant existence check failed, allowing onboarding to proceed");
      return false;
    }
  }

  async validateMerchantById(merchantId: string): Promise<MerchantValidationResult> {
    try {
      logger.info("Validating merchant by ID", { merchantId });

      const response = await this.apiClient.get(`/merchants/${merchantId}`);

      logger.info("Merchant validation response", {
        status: response.status,
        merchantId: response.data,
      });

      const merchant = response.data?.response?.data?.[0];

      if (!merchant) {
        return {
          isValid: false,
          error: "Merchant not found",
        };
      }

      const isActive = (merchant.status === 1 || merchant.status === 2) && merchant.inactive === 0 && merchant.frozen === 0;

      if (!isActive) {
        const statusInfo = {
          status: merchant.status,
          inactive: merchant.inactive,
          frozen: merchant.frozen,
          autoBoarded: merchant.autoBoarded,
        };

        logger.warn("Merchant status check failed", statusInfo);

        return {
          isValid: false,
          error: `Merchant is not active (status: ${merchant.status}, inactive: ${merchant.inactive}, frozen: ${merchant.frozen})`,
        };
      }

      if (merchant.mcc && !isApprovedMCCCode(merchant.mcc)) {
        const mccDetails = getMCCCodeDetails(merchant.mcc);
        logger.warn("Merchant MCC validation failed", {
          merchantId,
          mcc: merchant.mcc,
          mccDescription: mccDetails?.description,
        });

        return {
          isValid: false,
          error: `Merchant MCC code ${merchant.mcc} is not approved for payment processing. Only specific business categories are currently supported.`,
        };
      }

      logger.info("Merchant validation successful", {
        merchantId,
        status: merchant.status,
        statusType: merchant.status === 1 ? "fully-approved" : merchant.status === 2 ? "auto-boarded" : "unknown",
        inactive: merchant.inactive,
        frozen: merchant.frozen,
        autoBoarded: merchant.autoBoarded,
        dba: merchant.dba,
        mcc: merchant.mcc,
      });

      return {
        isValid: true,
        merchant,
      };
    } catch (error) {
      const axiosError = error as AxiosError;
      logger.error("Error validating merchant by ID", {
        merchantId,
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data,
        message: axiosError.message,
      });

      if (axiosError.response?.status === 404) {
        return {
          isValid: false,
          error: "Merchant not found",
        };
      }

      return {
        isValid: false,
        error: `Validation failed: ${axiosError.message}`,
      };
    }
  }

  async createMerchant(merchantData: OnboardingRequest): Promise<PayrixMerchantResponse> {
    try {
      logger.info("Sending request to Payrix API", {
        url: `${PAYRIX_API_URL}/entities`,
        headers: { "Content-Type": "application/json", APIKEY: "[REDACTED]" },
        merchantEmail: merchantData.email,
        merchantName: merchantData.name,
        hasBankVerification: !!merchantData.bankVerification,
        bankVerificationMethod: merchantData.bankVerification?.verificationMethod,
        // Sensitive data (SSN, bank details, file content) intentionally excluded for PCI compliance
        // data: merchantData,
      });

      logger.info("Merchant data check", {
        merchantData,
      });

      const response = await this.apiClient.post("/entities", merchantData);

      logger.info("Payrix API response", {
        status: response.status,
        data: response.data,
      });

      const entityData = response.data?.response?.data?.[0];
      if (!entityData) {
        throw new Error("Invalid Payrix response structure: no entity data found");
      }

      return entityData;
    } catch (error) {
      const axiosError = error as AxiosError;
      logger.error("Payrix API Error", {
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data,
        message: axiosError.message,
      });

      throw new Error(`Payrix API Error (${axiosError.response?.status}): ${JSON.stringify(axiosError.response?.data || axiosError.message)}`);
    }
  }

  async createNote(noteData: { entity: string; note: string; type?: string; login?: string }): Promise<{ id: string }> {
    try {
      logger.info("Creating note in Payrix", {
        entity: noteData.entity,
        type: noteData.type,
        noteLength: noteData.note.length,
      });

      const response = await this.apiClient.post("/notes", noteData);

      logger.info("Payrix note creation response", {
        status: response.status,
        data: response.data,
      });

      const noteData_response = response.data?.response?.data?.[0];
      if (!noteData_response?.id) {
        throw new Error("Invalid Payrix response structure: no note ID found");
      }

      return { id: noteData_response.id };
    } catch (error) {
      logger.error("Error creating note in Payrix", { error });
      if (error instanceof AxiosError) {
        logger.error("Payrix API error details", {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message,
        });
      }
      throw error;
    }
  }

  async createNoteDocument(documentData: {
    note: string;
    file: { filename: string; content: Buffer; contentType: string };
    description?: string;
  }): Promise<{ id: string }> {
    try {
      logger.info("Creating note document in Payrix", {
        noteId: documentData.note,
        fileName: documentData.file.filename,
        fileSize: documentData.file.content.length,
        contentType: documentData.file.contentType,
      });

      // Create FormData for file upload
      const formData = new FormData();

      formData.append("note", documentData.note);
      formData.append("file", documentData.file.content, {
        filename: documentData.file.filename,
        contentType: documentData.file.contentType,
      });

      if (documentData.description) {
        formData.append("description", documentData.description);
      }

      const response = await this.apiClient.post("/noteDocuments", formData, {
        headers: {
          ...formData.getHeaders(),
        },
      });

      logger.info("Payrix note document creation response", {
        status: response.status,
        data: response.data,
      });

      const documentResponse = response.data?.response?.data?.[0];
      if (!documentResponse?.id) {
        throw new Error("Invalid Payrix response structure: no document ID found");
      }

      return { id: documentResponse.id };
    } catch (error) {
      logger.error("Error creating note document in Payrix", { error });
      if (error instanceof AxiosError) {
        logger.error("Payrix API error details", {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message,
        });
      }
      throw error;
    }
  }
}
